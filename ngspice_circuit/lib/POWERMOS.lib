* simulator lang=pspice


* Company: SiliconMagic
* Project: 40V Power Device
* Component: 40V Power NMOS SPICE Model
* Version: 1.0
* Date: 2025-05-27
* Update: 2025-06-06
* Description: This model provides a SPICE representation for a 40V Power NMOS transistor,
*              including parasitic elements and behavioral capacitance models.
* 

* Model Parameters
.PARAM param_MINT_Vto=3.15017077840398
.PARAM param_MINT_Kp=66.97215546123455
.PARAM param_MINT_Nfs=440000000000
.PARAM param_MINT_Eta=1000
.PARAM param_MINT_Level=3
.PARAM param_MINT_L=1e-4
.PARAM param_MINT_W=1e-4
.PARAM param_MINT_Gamma=0
.PARAM param_MINT_Phi=0.6
.PARAM param_MINT_Is=1e-24
.PARAM param_MINT_Js=0
.PARAM param_MINT_Pb=0.8
.PARAM param_MINT_Cj=0
.PARAM param_MINT_Cjsw=0
.PARAM param_MINT_Cgso=0
.PARAM param_MINT_Cgdo=0
.PARAM param_MINT_Cgbo=0
.PARAM param_MINT_Tox=50e-09
.PARAM param_MINT_Xj=0
.PARAM param_MINT_U0=600.0
.PARAM param_MINT_Vmax=10000

.PARAM param_DBD_Bv=50.0
.PARAM param_DBD_Ibv=2.515978465E-004
.PARAM param_DBD_Rs=1E-6
.PARAM param_DBD_Is=2.74564811268e-12
.PARAM param_DBD_N=1
.PARAM param_DBD_M=0.55
.PARAM param_DBD_VJ=0.7
.PARAM param_DBD_Fc=0.5
.PARAM param_DBD_Cjo=8.7201532e-10
.PARAM param_DBD_Tt=1.05648165e-08

.PARAM param_DBGS_Bv=40.0
.PARAM param_DBGS_Ibv=0.86572u


.SUBCKT SWA04K006 D G S


.PARAM param_Cgs_0=7.714217890444044e-10
.PARAM param_Cgs_1=4.0280703320039185e-11
.PARAM param_Cgs_2=-1.9046913399780577e-11
.PARAM param_Cgs_3=7.613449317327738e-12
.PARAM param_Cgs_4=-2.158589829283146e-12
.PARAM param_Cgs_5=4.378948437899137e-13
.PARAM param_Cgs_6=-6.563253573101506e-14
.PARAM param_Cgs_7=7.426725097371452e-15
.PARAM param_Cgs_8=-6.360322778350816e-16
.PARAM param_Cgs_9=4.050401173895852e-17
.PARAM param_Cgs_10=-1.835878219157559e-18
.PARAM param_Cgs_11=5.320531326863741e-20
.PARAM param_Cgs_12=-6.173195592391248e-22
.PARAM param_Cgs_13=-1.804176360352064e-23
.PARAM param_Cgs_14=8.341693011018898e-25
.PARAM param_Cgs_15=-5.6068387326219925e-27
.PARAM param_Cgs_16=-4.642548510593262e-28
.PARAM param_Cgs_17=1.671500592963986e-29
.PARAM param_Cgs_18=-2.6865574380795886e-31
.PARAM param_Cgs_19=2.2301000238860847e-33
.PARAM param_Cgs_20=-7.772926433152147e-36
.FUNC Cgs(Vds) {param_Cgs_0 + param_Cgs_1 * Vds**1 + param_Cgs_2 * Vds**2 + param_Cgs_3 * Vds**3 + param_Cgs_4 * Vds**4 + param_Cgs_5 * Vds**5 + param_Cgs_6 * Vds**6 + param_Cgs_7 * Vds**7 + param_Cgs_8 * Vds**8 + param_Cgs_9 * Vds**9 + param_Cgs_10 * Vds**10 + param_Cgs_11 * Vds**11 + param_Cgs_12 * Vds**12 + param_Cgs_13 * Vds**13 + param_Cgs_14 * Vds**14 + param_Cgs_15 * Vds**15 + param_Cgs_16 * Vds**16 + param_Cgs_17 * Vds**17 + param_Cgs_18 * Vds**18 + param_Cgs_19 * Vds**19 + param_Cgs_20 * Vds**20}

.PARAM param_Cds_0=1.2852122069908754e-09
.PARAM param_Cds_1=-2.9188493920461405e-10
.PARAM param_Cds_2=2.1635950646854757e-10
.PARAM param_Cds_3=-1.2861570599511894e-10
.PARAM param_Cds_4=5.0506555019473515e-11
.PARAM param_Cds_5=-1.3334411909838581e-11
.PARAM param_Cds_6=2.4353637381836397e-12
.PARAM param_Cds_7=-3.142648251583372e-13
.PARAM param_Cds_8=2.894351829090603e-14
.PARAM param_Cds_9=-1.8932251460039867e-15
.PARAM param_Cds_10=8.524265825857386e-17
.PARAM param_Cds_11=-2.3863343414779638e-18
.PARAM param_Cds_12=2.4659482526305e-20
.PARAM param_Cds_13=8.64732353177672e-22
.PARAM param_Cds_14=-3.567779864022168e-23
.PARAM param_Cds_15=1.842376308523824e-25
.PARAM param_Cds_16=2.037463203169837e-26
.PARAM param_Cds_17=-6.868606283684063e-28
.PARAM param_Cds_18=1.0635980738309712e-29
.PARAM param_Cds_19=-8.575666505271257e-32
.PARAM param_Cds_20=2.916272242132033e-34
.FUNC Cds(Vds) {param_Cds_0 + param_Cds_1 * Vds**1 + param_Cds_2 * Vds**2 + param_Cds_3 * Vds**3 + param_Cds_4 * Vds**4 + param_Cds_5 * Vds**5 + param_Cds_6 * Vds**6 + param_Cds_7 * Vds**7 + param_Cds_8 * Vds**8 + param_Cds_9 * Vds**9 + param_Cds_10 * Vds**10 + param_Cds_11 * Vds**11 + param_Cds_12 * Vds**12 + param_Cds_13 * Vds**13 + param_Cds_14 * Vds**14 + param_Cds_15 * Vds**15 + param_Cds_16 * Vds**16 + param_Cds_17 * Vds**17 + param_Cds_18 * Vds**18 + param_Cds_19 * Vds**19 + param_Cds_20 * Vds**20}

.PARAM param_Cgd_0=3.8644326304379207e-10
.PARAM param_Cgd_1=-1.3443113511452803e-10
.PARAM param_Cgd_2=4.6879330716156526e-11
.PARAM param_Cgd_3=-1.1649017101116298e-11
.PARAM param_Cgd_4=1.4636741111032848e-12
.PARAM param_Cgd_5=9.292876620607331e-14
.PARAM param_Cgd_6=-7.358547702953661e-14
.PARAM param_Cgd_7=1.4317395299962158e-14
.PARAM param_Cgd_8=-1.6044091763913606e-15
.PARAM param_Cgd_9=1.1631793481562996e-16
.PARAM param_Cgd_10=-5.5175798801730534e-18
.PARAM param_Cgd_11=1.576888613721507e-19
.PARAM param_Cgd_12=-1.612039260853736e-21
.PARAM param_Cgd_13=-5.807093596670455e-23
.PARAM param_Cgd_14=2.3223650394159455e-24
.PARAM param_Cgd_15=-1.039474536102361e-26
.PARAM param_Cgd_16=-1.3348792921086872e-27
.PARAM param_Cgd_17=4.3391281661301184e-29
.PARAM param_Cgd_18=-6.544256507836825e-31
.PARAM param_Cgd_19=5.1518856681514076e-33
.PARAM param_Cgd_20=-1.7129559129517822e-35
.FUNC Cgd(Vds) {param_Cgd_0 + param_Cgd_1 * Vds**1 + param_Cgd_2 * Vds**2 + param_Cgd_3 * Vds**3 + param_Cgd_4 * Vds**4 + param_Cgd_5 * Vds**5 + param_Cgd_6 * Vds**6 + param_Cgd_7 * Vds**7 + param_Cgd_8 * Vds**8 + param_Cgd_9 * Vds**9 + param_Cgd_10 * Vds**10 + param_Cgd_11 * Vds**11 + param_Cgd_12 * Vds**12 + param_Cgd_13 * Vds**13 + param_Cgd_14 * Vds**14 + param_Cgd_15 * Vds**15 + param_Cgd_16 * Vds**16 + param_Cgd_17 * Vds**17 + param_Cgd_18 * Vds**18 + param_Cgd_19 * Vds**19 + param_Cgd_20 * Vds**20}


LD D D_int_L 2n
RD_L_PAR D D_int_L 0.05m
RLD1 D_int_L D_int_MOS 6e-06
RD D_int_MOS D_int_MOS_internal 0.001604688

LG G G_int_L 7.81292696075128e-10
RLG G G_int_L 1.96360271543439
RG G_int_L G_int_MOS 2.5

LS S S_int_L 4n
RS_L_PAR S S_int_L 0.05m
RLS1 S_int_L S_int_MOS 0.00055
RS S_int_MOS S_int_MOS_internal 0.001604688

M1 D_int_MOS_internal G_int_MOS S_int_MOS_internal S_int_MOS_internal MINT

DBGS S_int_MOS_internal G_int_MOS DBGS

DBD S_int_MOS_internal D_int_MOS_internal DBD


* C_gs S_int_MOS_internal G_int_MOS capacitor C = Cgs(V(D_int_MOS_internal, S_int_MOS_internal))
* C_ds D_int_MOS_internal S_int_MOS_internal capacitor C = Cds(V(D_int_MOS_internal, S_int_MOS_internal))
* C_gd D_int_MOS_internal G_int_MOS capacitor C = Cgd(V(D_int_MOS_internal, S_int_MOS_internal))
* 

.ENDS SWA04K006


.MODEL MINT NMOS(Vto={param_MINT_Vto} Kp={param_MINT_Kp} Nfs={param_MINT_Nfs} Eta={param_MINT_Eta}
+ Level={param_MINT_Level} L={param_MINT_L} W={param_MINT_W} Gamma={param_MINT_Gamma} Phi={param_MINT_Phi} Is={param_MINT_Is}
+ Js={param_MINT_Js} Pb={param_MINT_Pb} Cj={param_MINT_Cj} Cjsw={param_MINT_Cjsw} Cgso={param_MINT_Cgso} Cgdo={param_MINT_Cgdo} Cgbo={param_MINT_Cgbo}
+ Tox={param_MINT_Tox} Xj={param_MINT_Xj}
+ U0={param_MINT_U0} Vmax={param_MINT_Vmax} )

.MODEL DBD D(Bv={param_DBD_Bv} Ibv={param_DBD_Ibv} Rs={param_DBD_Rs} Is={param_DBD_Is}
+ N={param_DBD_N} M={param_DBD_M} VJ={param_DBD_VJ} Fc={param_DBD_Fc} Cjo={param_DBD_Cjo} Tt={param_DBD_Tt} )

.MODEL DBGS D(Bv={param_DBGS_Bv} Ibv={param_DBGS_Ibv})
